"""
数据转换模块
将原始 API 数据转换为优化格式，并生成 ECharts 配置
"""

import json
from typing import Dict, List, Any, Optional, Tu<PERSON>


def calculate_stats(entity_list: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    计算列表的统计信息

    Args:
        entity_list: 原始实体列表，格式 [{"id": "...", "name": "...", "value": ...}, ...]

    Returns:
        stats: 统计信息字典
    """
    if not entity_list:
        return {
            "total_count": 0,
            "positive_count": 0,
            "negative_count": 0,
            "avg": None,
            "max": None,
            "min": None
        }

    # 根据 id 去重，统计唯一主体数量
    unique_ids = set(entity["id"] for entity in entity_list if "id" in entity)
    total_count = len(unique_ids)

    # 提取所有值
    values = [entity["value"] for entity in entity_list if entity.get("value") is not None]

    if not values:
        return {
            "total_count": total_count,
            "positive_count": 0,
            "negative_count": 0,
            "avg": None,
            "max": None,
            "min": None
        }

    # 统计正负值数量
    positive_count = sum(1 for v in values if v > 0)
    negative_count = sum(1 for v in values if v < 0)

    # 计算平均值、最大值、最小值
    avg = sum(values) / len(values) if values else None
    max_value = max(values) if values else None
    min_value = min(values) if values else None

    return {
        "total_count": total_count,
        "positive_count": positive_count,
        "negative_count": negative_count,
        "avg": round(avg, 2) if avg is not None else None,
        "max": max_value,
        "min": min_value
    }


def filter_top_bottom(entity_list: List[Dict[str, Any]], n: int = 8) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    筛选正序前n和倒序前n的主体

    Args:
        entity_list: 原始实体列表
        n: 筛选数量（默认8）

    Returns:
        top_n: 正序前n（值从大到小）
        bottom_n: 倒序前n（值从小到大）
    """
    if not entity_list:
        return [], []

    # 过滤掉 value 为 None 的项
    valid_entities = [entity for entity in entity_list if entity.get("value") is not None]

    if not valid_entities:
        return [], []

    # 按 value 降序排列（正序）
    sorted_desc = sorted(valid_entities, key=lambda x: x["value"], reverse=True)

    # 按 value 升序排列（倒序）
    sorted_asc = sorted(valid_entities, key=lambda x: x["value"])

    # 提取前n个，只保留 name 和 value
    top_n = [
        {"name": item["name"], "value": item["value"]}
        for item in sorted_desc[:n]
    ]

    bottom_n = [
        {"name": item["name"], "value": item["value"]}
        for item in sorted_asc[:n]
    ]

    return top_n, bottom_n


def process_entity_list(raw_list: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    处理单个实体列表

    Args:
        raw_list: 原始列表数据

    Returns:
        processed: 优化后的列表结构
    """
    stats = calculate_stats(raw_list)
    top8, bottom8 = filter_top_bottom(raw_list, n=8)

    return {
        "stats": stats,
        "top8": top8,
        "bottom8": bottom8
    }


def transform_analysis_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    将原始接口数据转换为优化格式

    Args:
        raw_data: 原始 AnalysisDataDTO 数据

    Returns:
        optimized_data: 优化后的数据格式
    """
    # 1. 提取 timeTitle，优先从有数据的字段中获取
    time_title = None
    for key in ["ztshzbphzjdz", "ztzsbphzj", "ztfscpcdf", "ztscpcdf", "ztptdybdpcphfy"]:
        if key in raw_data and raw_data[key] and "timeTitle" in raw_data[key]:
            time_title = raw_data[key]["timeTitle"]
            break

    # 如果都没有，使用当前时间
    if not time_title:
        from datetime import datetime
        time_title = datetime.now().strftime("%Y-%m")

    # 2. 构建 summary
    summary = {}
    for key in ["ztshzbphzjdz", "ztzsbphzj", "ztfscpcdf", "ztscpcdf", "ztptdybdpcphfy"]:
        if key in raw_data and raw_data[key]:
            summary[key] = raw_data[key].get("value")
        else:
            summary[key] = None

    # 3. 处理各个实体列表
    optimized_data = {
        "timeTitle": time_title,
        "summary": summary,
        "gtzsbphzj": process_entity_list(raw_data.get("gtzsbphzj", [])),
        "gtfscpcdf": process_entity_list(raw_data.get("gtfscpcdf", [])),
        "gtscpcdf": process_entity_list(raw_data.get("gtscpcdf", [])),
        "gtptdybdpcphfy": process_entity_list(raw_data.get("gtptdybdpcphfy", []))
    }

    return optimized_data


def generate_echarts_config(list_data: Dict[str, Any], chart_title: str, series_name: str) -> Optional[str]:
    """
    生成 ECharts 配置

    Args:
        list_data: 列表数据（包含 stats, top8, bottom8）
        chart_title: 图表标题
        series_name: 系列名称

    Returns:
        echarts_config: ECharts 配置的 JSON 字符串，如果数据为空则返回 None
    """
    top8 = list_data.get("top8", [])
    bottom8 = list_data.get("bottom8", [])

    # 如果没有数据，不生成图表
    if not top8 and not bottom8:
        return None

    # 合并数据并排序（从小到大，bottom8在前，top8在后）
    all_data = []
    y_axis_data = []

    # 添加 bottom8 数据（负值，从小到大）
    for item in bottom8:
        all_data.append({
            "value": item["value"],
            "label": {"position": "right"},
            "itemStyle": {"color": "#32CD32"}
        })
        y_axis_data.append(item["name"])

    # 添加 top8 数据（正值，从小到大）
    for item in reversed(top8):  # 反转 top8 使其从小到大
        all_data.append({
            "value": item["value"],
            "label": {"position": "left"},
            "itemStyle": {"color": "#FF8C00"}
        })
        y_axis_data.append(item["name"])

    # 生成 ECharts 配置
    config = {
        "title": {
            "text": chart_title,
            "left": "center",
            "textStyle": {
                "fontSize": 16,
                "fontWeight": "bold"
            }
        },
        "tooltip": {
            "trigger": "axis",
            "axisPointer": {
                "type": "shadow"
            }
        },
        "grid": {
            "top": 80,
            "bottom": 30,
            "left": 20,
            "right": 20,
            "containLabel": True
        },
        "xAxis": {
            "type": "value",
            "position": "top",
            "splitLine": {
                "lineStyle": {
                    "type": "dashed"
                }
            },
            "axisLabel": {
                "formatter": "{value}"
            }
        },
        "yAxis": {
            "type": "category",
            "axisLine": {"show": False},
            "axisLabel": {"show": False},
            "axisTick": {"show": False},
            "splitLine": {"show": False},
            "data": y_axis_data
        },
        "series": [{
            "name": series_name,
            "type": "bar",
            "label": {
                "show": True,
                "fontSize": 12,
                "formatter": "{b}"
            },
            "data": all_data
        }]
    }

    # 将配置转换为 JSON 字符串
    config_str = json.dumps(config, ensure_ascii=False, indent=2)

    # 包装为完整的 ECharts 代码
    echarts_code = f"<echarts_code>option = {config_str};</echarts_code>"

    return echarts_code


def generate_all_echarts(optimized_data: Dict[str, Any]) -> List[str]:
    """
    生成所有 ECharts 配置

    Args:
        optimized_data: 优化后的数据

    Returns:
        echarts_configs: ECharts 配置列表
    """
    configs = []

    # 图表配置
    chart_configs = [
        {
            "key": "gtzsbphzj",
            "title": "阻塞不平衡资金分布（单位：万元）",
            "series_name": "阻塞不平衡资金"
        },
        {
            "key": "gtfscpcdf",
            "title": "非市场偏差电费分布（单位：万元）",
            "series_name": "非市场偏差电费"
        },
        {
            "key": "gtscpcdf",
            "title": "市场偏差电费分布（单位：万元）",
            "series_name": "市场偏差电费"
        }
    ]

    for chart_config in chart_configs:
        list_data = optimized_data.get(chart_config["key"], {})
        echarts_config = generate_echarts_config(
            list_data,
            chart_config["title"],
            chart_config["series_name"]
        )

        if echarts_config:  # 只添加非空的配置
            configs.append(echarts_config)

    return configs
