# 不平衡资金数据转换指导文档

## 概述

本文档说明如何将原始接口返回的 JSON 数据（`AnalysisDataDTO`）转换为大模型提示词所需的优化格式，以减少 token 消耗并提供必要的统计信息。

## 转换目标

- **减少冗余**：timeTitle 只在顶层出现一次，总体指标合并到 summary 对象
- **筛选数据**：每个列表只保留正序前8和倒序前8，而非全部主体
- **简化结构**：移除 id 字段，只保留 name 和 value
- **增加统计**：为每个列表添加统计信息（总数、平均值、正负分布等）

## 数据结构对比

### 原始结构（Raw JSON）
```json
{
  "ztshzbphzjdz": {
    "timeTitle": "2025-09",
    "value": -227307868.601
  },
  "ztzsbphzj": {
    "timeTitle": "2025-09",
    "value": -16330930.86137
  },
  "gtzsbphzj": [
    {
      "timeTitle": "2025-09",
      "id": "ff8080815417d9070154669098c906d9",
      "name": "阿克苏舒奇蒙光伏发电有限公司二期",
      "value": -134.37121
    },
    // ... 可能有上万条记录
  ]
}
```

### 优化后结构（Optimized JSON）
```json
{
  "timeTitle": "2025-09",
  "summary": {
    "ztshzbphzjdz": -227307868.601,
    "ztzsbphzj": -16330930.86137,
    "ztfscpcdf": -344498472.250,
    "ztscpcdf": 133521534.51,
    "ztptdybdpcphfy": null
  },
  "gtzsbphzj": {
    "stats": {
      "total_count": 3,
      "positive_count": 0,
      "negative_count": 3,
      "avg": -5443.64414,
      "max": -134.37121,
      "min": -5398.97372
    },
    "top8": [...],
    "bottom8": [...]
  }
}
```

## 转换步骤

### 步骤 1：提取顶层信息

从任一总体指标中提取 `timeTitle`，作为顶层字段：

```python
raw_data = get_analysis_data(time_title)
optimized_data = {
    "timeTitle": raw_data["ztshzbphzjdz"]["timeTitle"]
}
```

### 步骤 2：构建 summary 对象

将所有总体指标的 `value` 合并到 `summary` 对象：

```python
optimized_data["summary"] = {
    "ztshzbphzjdz": raw_data["ztshzbphzjdz"]["value"],
    "ztzsbphzj": raw_data["ztzsbphzj"]["value"],
    "ztfscpcdf": raw_data["ztfscpcdf"]["value"],
    "ztscpcdf": raw_data["ztscpcdf"]["value"],
    "ztptdybdpcphfy": raw_data["ztptdybdpcphfy"]["value"]
}
```

### 步骤 3：处理个体列表数据

对每个列表（`gtzsbphzj`、`gtfscpcdf`、`gtscpcdf`、`gtptdybdpcphfy`）执行以下操作：

#### 3.1 计算统计信息

```python
def calculate_stats(entity_list):
    """
    计算列表的统计信息
    
    Args:
        entity_list: 原始实体列表，格式 [{"id": "...", "name": "...", "value": ...}, ...]
    
    Returns:
        stats: 统计信息字典
    """
    if not entity_list:
        return {
            "total_count": 0,
            "positive_count": 0,
            "negative_count": 0,
            "avg": None,
            "max": None,
            "min": None
        }
    
    # 根据 id 去重，统计唯一主体数量
    unique_ids = set(entity["id"] for entity in entity_list)
    total_count = len(unique_ids)
    
    # 提取所有值
    values = [entity["value"] for entity in entity_list]
    
    # 统计正负值数量
    positive_count = sum(1 for v in values if v > 0)
    negative_count = sum(1 for v in values if v < 0)
    
    # 计算平均值、最大值、最小值
    avg = sum(values) / len(values) if values else None
    max_value = max(values) if values else None
    min_value = min(values) if values else None
    
    return {
        "total_count": total_count,
        "positive_count": positive_count,
        "negative_count": negative_count,
        "avg": round(avg, 2) if avg is not None else None,
        "max": max_value,
        "min": min_value
    }
```

#### 3.2 筛选 Top8 和 Bottom8

```python
def filter_top_bottom(entity_list, n=8):
    """
    筛选正序前n和倒序前n的主体
    
    Args:
        entity_list: 原始实体列表
        n: 筛选数量（默认8）
    
    Returns:
        top_n: 正序前n（值从大到小）
        bottom_n: 倒序前n（值从小到大）
    """
    if not entity_list:
        return [], []
    
    # 按 value 降序排列（正序）
    sorted_desc = sorted(entity_list, key=lambda x: x["value"], reverse=True)
    
    # 按 value 升序排列（倒序）
    sorted_asc = sorted(entity_list, key=lambda x: x["value"])
    
    # 提取前n个，只保留 name 和 value
    top_n = [
        {"name": item["name"], "value": item["value"]}
        for item in sorted_desc[:n]
    ]
    
    bottom_n = [
        {"name": item["name"], "value": item["value"]}
        for item in sorted_asc[:n]
    ]
    
    return top_n, bottom_n
```

#### 3.3 组装列表数据

```python
def process_entity_list(raw_list):
    """
    处理单个实体列表
    
    Args:
        raw_list: 原始列表数据
    
    Returns:
        processed: 优化后的列表结构
    """
    stats = calculate_stats(raw_list)
    top8, bottom8 = filter_top_bottom(raw_list, n=8)
    
    return {
        "stats": stats,
        "top8": top8,
        "bottom8": bottom8
    }
```

### 步骤 4：完整转换函数

```python
def transform_analysis_data(raw_data):
    """
    将原始接口数据转换为优化格式
    
    Args:
        raw_data: 原始 AnalysisDataDTO 数据
    
    Returns:
        optimized_data: 优化后的数据格式
    """
    # 1. 提取 timeTitle
    time_title = raw_data["ztshzbphzjdz"]["timeTitle"]
    
    # 2. 构建 summary
    summary = {
        "ztshzbphzjdz": raw_data["ztshzbphzjdz"]["value"],
        "ztzsbphzj": raw_data["ztzsbphzj"]["value"],
        "ztfscpcdf": raw_data["ztfscpcdf"]["value"],
        "ztscpcdf": raw_data["ztscpcdf"]["value"],
        "ztptdybdpcphfy": raw_data["ztptdybdpcphfy"]["value"]
    }
    
    # 3. 处理各个实体列表
    optimized_data = {
        "timeTitle": time_title,
        "summary": summary,
        "gtzsbphzj": process_entity_list(raw_data["gtzsbphzj"]),
        "gtfscpcdf": process_entity_list(raw_data["gtfscpcdf"]),
        "gtscpcdf": process_entity_list(raw_data["gtscpcdf"]),
        "gtptdybdpcphfy": process_entity_list(raw_data["gtptdybdpcphfy"])
    }
    
    return optimized_data
```

## 使用示例

```python
import json
import requests

# 1. 调用接口获取原始数据
response = requests.post(
    "http://your-api/xhyj/largeModel/getAnalysisData",
    json={"timeTitle": "2025-09"}
)
raw_data = response.json()["data"]

# 2. 转换为优化格式
optimized_data = transform_analysis_data(raw_data)

# 3. 序列化为 JSON 字符串用于大模型提示词
prompt_variable = json.dumps(optimized_data, ensure_ascii=False, indent=4)

# 4. 替换提示词模板中的占位符
with open("prompt_template_unbalance_analysis.md", "r", encoding="utf-8") as f:
    prompt_template = f.read()

final_prompt = prompt_template.replace("{{INPUT_DATA}}", prompt_variable)

# 5. 调用大模型
llm_response = call_llm_api(final_prompt)
```

## 数据压缩效果

以实际场景为例：

| 项目 | 原始数据 | 优化后 | 压缩比 |
|------|---------|--------|--------|
| 阻塞主体列表 | 10,000条 | 16条（top8+bottom8） | 99.84% |
| 非市场偏差列表 | 5,000条 | 16条 | 99.68% |
| 市场偏差列表 | 8,000条 | 16条 | 99.80% |
| **总体** | **23,000+条** | **48条+统计信息** | **约99.8%** |

**Token 节省**：假设每条记录平均消耗 50 tokens，可节省约 **1,147,600 tokens**！

## 注意事项

1. **空列表处理**：如果某个列表为空（如 `gtptdybdpcphfy`），`stats` 中的数值应设为 `null`，`top8` 和 `bottom8` 为空数组 `[]`

2. **去重逻辑**：统计 `total_count` 时应基于 `id` 字段去重，避免重复计数

3. **精度控制**：`avg` 字段建议保留2位小数，与提示词模板中的数值精度要求一致

4. **排序逻辑**：
   - `top8`：按 value **降序**（从大到小），代表"数值最高"的主体
   - `bottom8`：按 value **升序**（从小到大），代表"数值最低"的主体

5. **数据校验**：转换后应验证 `summary` 中的总计值与列表统计值的一致性：
   ```python
   assert abs(summary["ztzsbphzj"] - sum([item["value"] for item in raw_data["gtzsbphzj"]])) < 0.01
   ```

## 完整代码示例

完整的转换代码已保存在：`data_transformer.py`

可直接使用：
```bash
python data_transformer.py --input raw_data.json --output optimized_data.json
```

## 更新提示词模板

转换完成后，需要更新提示词模板中的 `<input>` 示例，使用优化后的格式：

```markdown
<input>
{
    "timeTitle": "2025-09",
    "summary": { ... },
    "gtzsbphzj": { "stats": {...}, "top8": [...], "bottom8": [...] },
    ...
}
</input>
```

确保示例与实际输入格式保持一致。

