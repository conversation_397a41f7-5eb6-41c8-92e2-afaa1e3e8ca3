[合法的echarts示例](https://echarts.apache.org/examples/zh/editor.html?c=bar-negative2)


```response

# analysis 流式回复节选 前面有更多 message ……

id: 1074
event: message
data: {"type":"token","content":"合计50","session_id":"1762831109811","timestamp":"2025-11-11T03:19:59.748445","id":"1074","event":"message"}

id: 1075
event: message
data: {"type":"token","content":"个主体","session_id":"1762831109811","timestamp":"2025-11-11T03:19:59.791164","id":"1075","event":"message"}

id: 1076
event: message
data: {"type":"token","content":"需关注","session_id":"1762831109811","timestamp":"2025-11-11T03:19:59.792498","id":"1076","event":"message"}

id: 1077
event: end
data: {"type":"end","content":"","session_id":"1762831109811","timestamp":"2025-11-11T03:19:59.825261","id":"1077","event":"end"}

# 假设 LLM 已经回复完成，下面是研发增加的 echarts 代码，以流式回复呈现
id: 1078
event: echarts
data: {"type":"echarts","content":"<echarts_code>阻塞不平衡资金列表图表，正序前8和倒序前8</echarts_code>","session_id":"1762831109811","timestamp":"2025-11-11T03:19:59.792498","id":"1078","event":"echarts"}

id: 1079    
event: echarts
data: {"type":"echarts","content":"<echarts_code>非市场偏差电费列表图表，正序前8和倒序前8</echarts_code>","session_id":"1762831109811","timestamp":"2025-11-11T03:19:59.792498","id":"1079","event":"echarts"}

# 假设把 echars 代码塞进去之后，应该是下面这样
id: 1080
event: echarts
data: {"type":"echarts","content":"<echarts_code>const labelRight = {\n  position: 'right'\n};\n\nconst labelLeft = {\n  position: 'left'\n};\n\noption = {\n  title: {\n    text: '阻塞不平衡资金分布（单位：万元）',\n    left: 'center',\n    textStyle: {\n      fontSize: 16,\n      fontWeight: 'bold'\n    }\n  },\n  tooltip: {\n    trigger: 'axis',\n    axisPointer: {\n      type: 'shadow'\n    },\n    formatter: function (params) {\n      let item = params[0];\n      let value = (item.value / 10000).toFixed(2);\n      return item.name + '<br/>' + '费用: ' + value + ' 万元';\n    }\n  },\n  grid: {\n    top: 80,\n    bottom: 30,\n    left: 20,\n    right: 20,\n    containLabel: true\n  },\n  xAxis: {\n    type: 'value',\n    position: 'top',\n    splitLine: {\n      lineStyle: {\n        type: 'dashed'\n      }\n    },\n    axisLabel: {\n      formatter: function (value) {\n        return (value / 10000).toFixed(0) + '万';\n      }\n    }\n  },\n  yAxis: {\n    type: 'category',\n    axisLine: { show: false },\n    axisLabel: { show: false },\n    axisTick: { show: false },\n    splitLine: { show: false },\n    data: [\n      '某煤化工基地P',\n      '某钢铁厂O',\n      '某数据中心N',\n      '某电解铝厂M',\n      '某化工企业L',\n      '某工业园区K',\n      '某分布式光伏J',\n      '某小型电厂I',\n      '某水电站H',\n      '某燃气电厂G',\n      '某供热机组F',\n      '某风电场E',\n      '某光伏电站D',\n      '某区域电厂C',\n      '某新能源基地B',\n      '某大型火电厂A'\n    ]\n  },\n  series: [\n    {\n      name: '阻塞不平衡资金',\n      type: 'bar',\n      label: {\n        show: true,\n        fontSize: 12,\n        formatter: '{b}'\n      },\n      data: [\n        { value: -3456789.01, label: labelRight, itemStyle: { color: '#32CD32' } },\n        { value: -2345678.9, label: labelRight, itemStyle: { color: '#32CD32' } },\n        { value: -1234567.89, label: labelRight, itemStyle: { color: '#32CD32' } },\n        { value: -987654.32, label: labelRight, itemStyle: { color: '#32CD32' } },\n        { value: -678901.23, label: labelRight, itemStyle: { color: '#32CD32' } },\n        { value: -456789.01, label: labelRight, itemStyle: { color: '#32CD32' } },\n        { value: -234567.89, label: labelRight, itemStyle: { color: '#32CD32' } },\n        { value: -123456.78, label: labelRight, itemStyle: { color: '#32CD32' } },\n        { value: 234567.89, label: labelLeft, itemStyle: { color: '#FF8C00' } },\n        { value: 456789.01, label: labelLeft, itemStyle: { color: '#FF8C00' } },\n        { value: 678901.23, label: labelLeft, itemStyle: { color: '#FF8C00' } },\n        { value: 987654.32, label: labelLeft, itemStyle: { color: '#FF8C00' } },\n        { value: 1876543.21, label: labelLeft, itemStyle: { color: '#FF8C00' } },\n        { value: 2345678.9, label: labelLeft, itemStyle: { color: '#FF8C00' } },\n        { value: 3876543.21, label: labelLeft, itemStyle: { color: '#FF8C00' } },\n        { value: 4567890.12, label: labelLeft, itemStyle: { color: '#FF8C00' } }\n      ]\n    }\n  ]\n};</echarts_code>","session_id":"1762831109811","timestamp":"2025-11-11T03:19:59.792498","id":"1080","event":"echarts"}

```


id 1080 对应的echarts代码

渲染之后长这样：[链接](https://echarts.apache.org/examples/zh/editor.html?c=bar-negative2&code=xVdbTxtHFH7nV4zUhwU1WXxJwDhtpYSU3mgbNZH6EPGwtgdYde2xdocGGiGVOhRQIdCGRA2hcSlpkypy5TZEMcYhf8bjyxN_oWd2Zi9DbFVqiOMHa-fcvnP55qydJjmHIstIYesLc2qaonfR9T6E8sQxqUlySaTZXKz1zZ_r6wsZj-PJDrYWSIUpyXORtKAmtXDSfYQDnqVg2v75gO3cr1fW2P6T1s5O6-mN9tKPbPl7Vikc1ZbZ2u3687Wj2la9ssQWQbKinXK9OQJ4p3GOYlvKeMTLdC6AQGiS5Ohl8xuQRIeEkZB9iXk1ECBFrIzmKubhe57bUEIsaub9PKHwKWyDrTFrOhKKP14iJgcPwOhcHoA0Z9rIkGsyqDCfJHbWoK7x5Ewu7bakP2_YRtYZ8N0tTJFJcRaaJVRXIxPnQrqvDWsGg7KfG-niNIiiEfgM6JSMmbM40x8b8FxsTGfsnBtRzxlZjN5G2jspe_A9jT-19vabm48gWziIUCBEosuaCOE3ZMo2M343CDQmERFlpQilJJtEcXkWQ4nJk0uY4AikoYaZG-esSUJXZ7AXf_Y8dNMHEE10c5LNDhEL4KXQyVsmHTdzoWFbcDo2fz9exnCmsZy0KM0fDp-lzCqgTYd5uSkF4_I7LBQvjyIywHsKLZX99GG9uuc61J02KJ4i9lyIZ7JI5EyTa5CPYTm4U-rd9FfM9Fdd1OEedtJnDGok0VWZvdYobjQXH7DVO-zZ76xYZdvlSzJNoWz_9Fv71gK7-d3nirhxu9xY-6teKbEXhc8UTXPzaevhbvvWL-DzqaIRIPXaQr2yNa5qQFzZYvdKbLX6iapxlwarrbPFlXpt_WNVWV5n938AQID6SE2vvAfi5uO7H6q5LRUa5U3h8IGiqR_eaxZKje1q8-DGmNqA3ZvcYbv6vort5iNALh4rs8qKRQEyqmZ1p9wqPG9UN0SjL6huDx7yYhYeC8_zgtYTHq8cbJsYiCUG5_GV74Bu-9aPLlmYMrydCrdKvRoQ3iUKv8CeibJnY2Gpf42066l5__Z5Fiq_EBocRBfclZJAp1Frr8i-rR3VVhvFv1lpg20_Ys_-aB3uc8mvS83SIZesP3Ely82DF62Vf-D14McK8kViwSXR6fiZs0PDiRE9Eg1y9AsM3n1hJV-f3k6BFWYRXstb8djoxXhMkxdaqakzckxA6yO9Bo4KZD3Rc-SRxPDQ2TN6PMSH3gDzEUeieizea-A3zK43MGPBLn048TqBYStcIXm-Ehql3eMrQS6A0EqQS2K58efd4yvByzvol5Is_y17qkuGY2OJ0UgEMlRa4gUMJn9CAQMOn1DA4Da-YsCXWRB1Q8f1WFfeuyj_SQIfryP71NnxTXrihcR7WojgTESHF-b_qUQG5C998ZNyAv5v_Qs&enc=deflate)：

```echarts

const labelRight = {
  position: 'right'
};

const labelLeft = {
  position: 'left'
};

option = {
  title: {
    text: '阻塞不平衡资金分布（单位：万元）',
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'bold'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function (params) {
      let item = params[0];
      let value = (item.value / 10000).toFixed(2);
      return item.name + '<br/>' + '费用: ' + value + ' 万元';
    }
  },
  grid: {
    top: 80,
    bottom: 30,
    left: 20,
    right: 20,
    containLabel: true
  },
  xAxis: {
    type: 'value',
    position: 'top',
    splitLine: {
      lineStyle: {
        type: 'dashed'
      }
    },
    axisLabel: {
      formatter: function (value) {
        return (value / 10000).toFixed(0) + '万';
      }
    }
  },
  yAxis: {
    type: 'category',
    axisLine: { show: false },
    axisLabel: { show: false },
    axisTick: { show: false },
    splitLine: { show: false },
    data: [
      '某煤化工基地P',
      '某钢铁厂O',
      '某数据中心N',
      '某电解铝厂M',
      '某化工企业L',
      '某工业园区K',
      '某分布式光伏J',
      '某小型电厂I',
      '某水电站H',
      '某燃气电厂G',
      '某供热机组F',
      '某风电场E',
      '某光伏电站D',
      '某区域电厂C',
      '某新能源基地B',
      '某大型火电厂A'
    ]
  },
  series: [
    {
      name: '阻塞不平衡资金',
      type: 'bar',
      label: {
        show: true,
        fontSize: 12,
        formatter: '{b}'
      },
      data: [
        // Bottom8 - 负值，柱子在左边，标签在右边（绿色）
        {
          value: -3456789.01,
          label: labelRight,
          itemStyle: { color: '#32CD32' }
        },
        {
          value: -2345678.9,
          label: labelRight,
          itemStyle: { color: '#32CD32' }
        },
        {
          value: -1234567.89,
          label: labelRight,
          itemStyle: { color: '#32CD32' }
        },
        {
          value: -987654.32,
          label: labelRight,
          itemStyle: { color: '#32CD32' }
        },
        {
          value: -678901.23,
          label: labelRight,
          itemStyle: { color: '#32CD32' }
        },
        {
          value: -456789.01,
          label: labelRight,
          itemStyle: { color: '#32CD32' }
        },
        {
          value: -234567.89,
          label: labelRight,
          itemStyle: { color: '#32CD32' }
        },
        {
          value: -123456.78,
          label: labelRight,
          itemStyle: { color: '#32CD32' }
        },
        // Top8 - 正值，柱子在右边，标签在左边（橙色）
        { value: 234567.89, label: labelLeft, itemStyle: { color: '#FF8C00' } },
        { value: 456789.01, label: labelLeft, itemStyle: { color: '#FF8C00' } },
        { value: 678901.23, label: labelLeft, itemStyle: { color: '#FF8C00' } },
        { value: 987654.32, label: labelLeft, itemStyle: { color: '#FF8C00' } },
        {
          value: 1876543.21,
          label: labelLeft,
          itemStyle: { color: '#FF8C00' }
        },
        { value: 2345678.9, label: labelLeft, itemStyle: { color: '#FF8C00' } },
        {
          value: 3876543.21,
          label: labelLeft,
          itemStyle: { color: '#FF8C00' }
        },
        { value: 4567890.12, label: labelLeft, itemStyle: { color: '#FF8C00' } }
      ]
    }
  ]
};

```
