# Chat Stream Analysis V2 接口说明

## 概述

`chat_stream_analysis_v2` 是一个新的大模型分析接口，在原有 `chat_stream_analysis` 基础上增加了以下功能：

1. **新的数据源**: 从 `getAnalysisData` 接口获取数据
2. **数据优化**: 将原始数据转换为优化格式，减少 token 消耗
3. **ECharts 图表**: 在流式响应结束后自动生成 3 个图表

## 接口地址

```
POST /api/chat/stream/analysis/v2
```

## 请求格式

```json
{
  "message": {
    "timeTitle": "2025-09",
    "其他参数": "可选"
  },
  "session_id": "可选的会话ID"
}
```

## 响应格式

接口返回 Server-Sent Events (SSE) 流式响应：

### 1. 文本消息阶段
```
id: 1
event: message
data: {"type":"token","content":"分析","session_id":"xxx","timestamp":"xxx","id":"1","event":"message"}

id: 2
event: message
data: {"type":"token","content":"结果","session_id":"xxx","timestamp":"xxx","id":"2","event":"message"}
```

### 2. 结束信号
```
id: 100
event: end
data: {"type":"end","content":"","session_id":"xxx","timestamp":"xxx","id":"100","event":"end"}
```

### 3. ECharts 图表阶段
```
id: 101
event: echarts
data: {"type":"echarts","content":"<echarts_code>option = {...};</echarts_code>","session_id":"xxx","timestamp":"xxx","id":"101","event":"echarts"}

id: 102
event: echarts
data: {"type":"echarts","content":"<echarts_code>option = {...};</echarts_code>","session_id":"xxx","timestamp":"xxx","id":"102","event":"echarts"}

id: 103
event: echarts
data: {"type":"echarts","content":"<echarts_code>option = {...};</echarts_code>","session_id":"xxx","timestamp":"xxx","id":"103","event":"echarts"}
```

## 图表说明

接口会自动生成 3 个 ECharts 图表：

1. **阻塞不平衡资金分布图** (`gtzsbphzj`)
2. **非市场偏差电费分布图** (`gtfscpcdf`) 
3. **市场偏差电费分布图** (`gtscpcdf`)

每个图表显示：
- Top8: 费用最高的 8 个主体（橙色柱子）
- Bottom8: 费用最低的 8 个主体（绿色柱子）

## 环境配置

在 `.env` 文件中需要配置：

```env
# 新的分析数据接口
ANALYSIS_DATA_API_URL=https://mdsaxjdev.netts-dev.tsintergy.com/web/xhyj/largeModel/getAnalysisData
```

## 数据流程

1. **接口调用**: 从 `ANALYSIS_DATA_API_URL` 获取原始数据
2. **数据转换**: 将原始数据转换为优化格式（参考 `04-数据转换指导文档.md`）
3. **LLM 处理**: 使用优化后的数据调用大模型
4. **流式响应**: 返回大模型的流式文本响应
5. **图表生成**: 基于优化数据生成 ECharts 配置并流式返回

## 测试方法

### 1. 启动服务器
```bash
python3 start_server_test.py
```

### 2. 测试数据转换功能
```bash
python3 test_data_transformer.py
```

### 3. 测试完整接口
```bash
python3 test_api_v2.py
```

## 文件说明

- `chat_api.py`: 主要 API 文件，包含新接口
- `data_transformer.py`: 数据转换和 ECharts 生成模块
- `test_data_transformer.py`: 数据转换功能测试
- `test_api_v2.py`: 完整接口测试
- `start_server_test.py`: 测试服务器启动脚本

## 性能优化

相比原始数据，优化后的数据可以节省约 99.8% 的 token 消耗：

- 原始数据: 可能包含上万条记录
- 优化后: 每个列表只保留 Top8 + Bottom8 = 16 条记录
- 额外增加: 统计信息（总数、平均值、正负分布等）

## 错误处理

接口包含完整的错误处理机制：

1. **数据接口调用失败**: 返回 500 错误
2. **数据转换失败**: 返回 500 错误  
3. **ECharts 生成失败**: 返回 500 错误
4. **LLM 调用失败**: 在流式响应中返回错误事件

## 日志记录

接口会记录详细的性能日志：

- 各个步骤的执行时间
- 数据大小统计
- 图表生成数量
- 总执行时间

调试信息会保存到 `requestM_v2.txt` 文件中。
