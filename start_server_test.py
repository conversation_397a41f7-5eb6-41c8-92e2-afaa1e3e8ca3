#!/usr/bin/env python3
"""
启动测试服务器
"""

import uvicorn
import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    print("启动测试服务器...")
    print("访问地址: http://localhost:8000")
    print("新接口: POST http://localhost:8000/api/chat/stream/analysis/v2")
    print("按 Ctrl+C 停止服务器")
    print()
    
    try:
        uvicorn.run(
            "chat_api:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n服务器已停止")
