#!/usr/bin/env python3
"""
测试 chat_stream_analysis_v2 接口
"""

import asyncio
import httpx
import json

async def test_chat_stream_analysis_v2():
    """测试新的 v2 接口"""
    
    # 测试数据
    test_request = {
        "message": {
            "timeTitle": "2025-09",
            "testData": True
        },
        "session_id": "test_session_v2"
    }
    
    print("=== 测试 chat_stream_analysis_v2 接口 ===")
    print(f"请求数据: {json.dumps(test_request, ensure_ascii=False, indent=2)}")
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            # 发送请求到新接口
            async with client.stream(
                "POST",
                "http://localhost:8000/api/chat/stream/analysis/v2",
                json=test_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status_code != 200:
                    print(f"❌ 请求失败，状态码: {response.status_code}")
                    content = await response.aread()
                    print(f"错误内容: {content.decode()}")
                    return
                
                print("✅ 开始接收流式响应...")
                
                message_count = 0
                echarts_count = 0
                
                async for line in response.aiter_lines():
                    if line.strip():
                        print(f"收到数据: {line}")
                        
                        # 解析 SSE 格式
                        if line.startswith("data: "):
                            try:
                                data_str = line[6:]  # 移除 "data: " 前缀
                                data = json.loads(data_str)
                                
                                if data.get("type") == "token":
                                    message_count += 1
                                elif data.get("type") == "echarts":
                                    echarts_count += 1
                                    print(f"📊 收到第 {echarts_count} 个图表配置")
                                elif data.get("type") == "end":
                                    print("🏁 流式响应结束")
                                elif data.get("type") == "error":
                                    print(f"❌ 收到错误: {data.get('content')}")
                                    
                            except json.JSONDecodeError as e:
                                print(f"⚠️ JSON 解析失败: {e}")
                
                print(f"\n📊 统计信息:")
                print(f"  消息 token 数量: {message_count}")
                print(f"  ECharts 图表数量: {echarts_count}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")

async def main():
    """主函数"""
    print("开始测试 chat_stream_analysis_v2 接口...")
    print("注意: 请确保服务器正在运行 (python3 chat_api.py)")
    print()
    
    await test_chat_stream_analysis_v2()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    asyncio.run(main())
