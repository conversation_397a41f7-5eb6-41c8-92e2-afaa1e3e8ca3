#!/usr/bin/env python3
"""
测试数据转换功能
"""

import json
from data_transformer import transform_analysis_data, generate_all_echarts

# 测试数据（基于 test/02-data_example.json）
test_data = {
    "ztshzbphzjdz": {
        "timeTitle": "2025-09",
        "value": -227307868.601
    },
    "ztzsbphzj": {
        "timeTitle": "2025-09",
        "value": -16330930.86137
    },
    "ztfscpcdf": {
        "timeTitle": "2025-09",
        "value": -344498472.250
    },
    "ztscpcdf": {
        "timeTitle": "2025-09",
        "value": 133521534.51
    },
    "ztptdybdpcphfy": {
        "timeTitle": "2025-09",
        "value": None
    },
    "gtzsbphzj": [
        {
            "timeTitle": "2025-09",
            "id": "ff8080815417d9070154669098c906d9",
            "name": "阿克苏舒奇蒙光伏发电有限公司二期",
            "value": -134.37121
        },
        {
            "timeTitle": "2025-09",
            "id": "ff8080815417d907015466a13ab206e0",
            "name": "新疆华电和田光伏发电有限责任公司二期",
            "value": -5398.97372
        }
    ],
    "gtfscpcdf": [
        {
            "timeTitle": "2025-09",
            "id": "0097fed2-7c98-4dce-b1e7-670e1ca3554c",
            "name": "华电新疆天润托里新能源有限公司（华电天润老风口风电场）",
            "value": -81512.49
        },
        {
            "timeTitle": "2025-09",
            "id": "00c407d3-1b43-4d49-a0e1-79cf30f01139",
            "name": "新疆华电达坂城新能源有限公司（华电达坂城西沟风电三场）",
            "value": 341165.18
        }
    ],
    "gtscpcdf": [
        {
            "timeTitle": "2025-09",
            "id": "0097fed2-7c98-4dce-b1e7-670e1ca3554c",
            "name": "华电新疆天润托里新能源有限公司（华电天润老风口风电场）",
            "value": 190539.750
        },
        {
            "timeTitle": "2025-09",
            "id": "00c407d3-1b43-4d49-a0e1-79cf30f01139",
            "name": "新疆华电达坂城新能源有限公司（华电达坂城西沟风电三场）",
            "value": -405181.750
        }
    ],
    "gtptdybdpcphfy": []
}

def test_transform_analysis_data():
    """测试数据转换功能"""
    print("=== 测试数据转换功能 ===")
    
    try:
        optimized_data = transform_analysis_data(test_data)
        print("✅ 数据转换成功")
        print(f"时间标题: {optimized_data['timeTitle']}")
        print(f"汇总数据: {optimized_data['summary']}")
        
        # 检查各个列表的统计信息
        for key in ["gtzsbphzj", "gtfscpcdf", "gtscpcdf", "gtptdybdpcphfy"]:
            list_data = optimized_data[key]
            stats = list_data['stats']
            print(f"\n{key} 统计:")
            print(f"  总数: {stats['total_count']}")
            print(f"  正值数: {stats['positive_count']}")
            print(f"  负值数: {stats['negative_count']}")
            print(f"  平均值: {stats['avg']}")
            print(f"  Top8 数量: {len(list_data['top8'])}")
            print(f"  Bottom8 数量: {len(list_data['bottom8'])}")
        
        return optimized_data
        
    except Exception as e:
        print(f"❌ 数据转换失败: {e}")
        return None

def test_generate_echarts(optimized_data):
    """测试 ECharts 配置生成"""
    print("\n=== 测试 ECharts 配置生成 ===")
    
    try:
        echarts_configs = generate_all_echarts(optimized_data)
        print(f"✅ ECharts 配置生成成功，共生成 {len(echarts_configs)} 个图表")
        
        for i, config in enumerate(echarts_configs):
            print(f"\n图表 {i+1} 预览:")
            # 只显示前200个字符
            preview = config[:200] + "..." if len(config) > 200 else config
            print(preview)
        
        return echarts_configs
        
    except Exception as e:
        print(f"❌ ECharts 配置生成失败: {e}")
        return None

def main():
    """主测试函数"""
    print("开始测试数据转换模块...")
    
    # 测试数据转换
    optimized_data = test_transform_analysis_data()
    if not optimized_data:
        return
    
    # 测试 ECharts 生成
    echarts_configs = test_generate_echarts(optimized_data)
    if not echarts_configs:
        return
    
    print("\n=== 测试完成 ===")
    print("所有功能测试通过！")

if __name__ == "__main__":
    main()
