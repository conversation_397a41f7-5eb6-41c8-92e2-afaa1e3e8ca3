# OpenAI配置 - 指向本地的openai_proxy服务
OPENAI_API_KEY=sk-Rgl2WSVTaxg5Bsaq425999Fa22A147D2B292D374CaC61223
OPENAI_API_BASE=http://127.0.0.1:8000/v1/chat/completions
OPENAI_MODEL_NAME=deepseek-v3.1-250821
OPENAI_MAX_TOKENS=16000
OPENAI_TEMPERATURE=0.7
# 实际大模型服务地址（openai_proxy会转发到这里）
LLM_API_URL=https://api.gpt.ge/v1/chat/completions
# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8801
DEBUG=True
# 数据接口配置
DATA_API_URL=https://mdsaxjdev.netts-dev.tsintergy.com/web/xhyj/largeModel/getAfterwardData
# 新的分析数据接口
ANALYSIS_DATA_API_URL=https://mdsaxjdev.netts-dev.tsintergy.com/web/xhyj/largeModel/getAnalysisData